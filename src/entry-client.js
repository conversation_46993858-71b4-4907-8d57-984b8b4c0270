import { createApp } from './main'
import { getAsyncData } from './store/getAsyncData'

const { app, router, store } = createApp()

// 在挂载应用之前先恢复服务端状态
if (window.__INIT_STATE__) {
  try {
    // 确保状态结构正确
    if (window.__INIT_STATE__._state && window.__INIT_STATE__._state.data) {
      store.replaceState(window.__INIT_STATE__._state.data)
      console.log('SSR state hydrated successfully')
    } else if (window.__INIT_STATE__.state) {
      // 兼容不同的状态结构
      store.replaceState(window.__INIT_STATE__.state)
      console.log('SSR state hydrated successfully (legacy format)')
    } else {
      console.warn('Invalid state structure in window.__INIT_STATE__')
    }
  } catch (error) {
    console.error('Failed to hydrate store state:', error)
  }
} else {
  // 纯客户端模式：没有 SSR 状态，需要主动获取数据
  console.log('Client-side rendering mode: loading initial data')
}

// 客户端路由守卫：在路由切换时获取数据
router.beforeResolve(async (to, from) => {
  // 如果没有 SSR 状态，或者路由发生变化，需要获取数据
  const needsData = !window.__INIT_STATE__ || to.path !== from.path

  if (needsData) {
    console.log('Loading data for route:', to.path)
    try {
      await getAsyncData(router, store, false)
      console.log('Client-side data loaded successfully')
    } catch (error) {
      console.error('Failed to load client-side data:', error)
    }
  }
})

// 针对有懒加载路由组件的情况，需等待路由解析完
router.isReady().then(async () => {
  // 添加错误监听
  app.config.errorHandler = (err, instance, info) => {
    console.error('Vue error:', err, info)
  }

  // 检查是否是 SSR 模式还是纯客户端模式
  const isSSRMode = !!window.__INIT_STATE__

  if (isSSRMode) {
    // SSR 模式：进行 hydration
    console.log('SSR mode: performing hydration')
    app.mount('#app', true) // 第二个参数 true 表示 hydration
  } else {
    // 纯客户端模式：清空容器并进行客户端渲染
    console.log('Client-side rendering mode: performing client render')

    // 清空服务端可能存在的内容
    const appContainer = document.getElementById('app')
    if (appContainer) {
      appContainer.innerHTML = ''
    }

    // 在挂载前获取初始数据
    try {
      await getAsyncData(router, store, false)
      console.log('Initial client-side data loaded')
    } catch (error) {
      console.error('Failed to load initial data:', error)
    }

    // 进行纯客户端渲染（不是 hydration）
    app.mount('#app', false) // 第二个参数 false 表示不进行 hydration
  }

  // 验证挂载是否成功
  if (process.env.NODE_ENV === 'development') {
    console.log(`Client app mounted successfully (${isSSRMode ? 'SSR' : 'CSR'} mode)`)
    console.log('Store state:', store.state)
    console.log('Current route:', router.currentRoute.value.path)
  }
})