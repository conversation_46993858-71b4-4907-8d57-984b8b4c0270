import { createApp } from './main'

const { app, router, store } = createApp()

// 在挂载应用之前先恢复服务端状态
if (window.__INIT_STATE__) {
  try {
    // 确保状态结构正确
    if (window.__INIT_STATE__._state && window.__INIT_STATE__._state.data) {
      store.replaceState(window.__INIT_STATE__._state.data)
    } else if (window.__INIT_STATE__.state) {
      // 兼容不同的状态结构
      store.replaceState(window.__INIT_STATE__.state)
    } else {
      console.warn('Invalid state structure in window.__INIT_STATE__')
    }
  } catch (error) {
    console.error('Failed to hydrate store state:', error)
  }
}

// 针对有懒加载路由组件的情况，需等待路由解析完
router.isReady().then(() => {
  // 添加hydration错误监听
  app.config.errorHandler = (err, instance, info) => {
    if (info.includes('hydration')) {
      console.error('Hydration error:', err, info)
    }
  }

  app.mount('#app')

  // 验证hydration是否成功
  if (process.env.NODE_ENV === 'development') {
    console.log('Client hydration completed')
    console.log('Store state:', store.state)
  }
})