#!/usr/bin/env node

/**
 * Simple test script to verify hydration fixes
 * Run this after starting your SSR server to check for hydration issues
 */

const puppeteer = require('puppeteer');

async function testHydration() {
  console.log('🧪 Testing hydration...');
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for CI
      devtools: true 
    });
    
    const page = await browser.newPage();
    
    // Listen for console messages
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('hydration') || text.includes('mismatch')) {
        console.log(`🔍 Console: ${text}`);
      }
    });
    
    // Listen for errors
    page.on('pageerror', error => {
      console.error(`❌ Page error: ${error.message}`);
    });
    
    // Navigate to your SSR server
    console.log('📍 Navigating to http://localhost:8887');
    await page.goto('http://localhost:8887', { 
      waitUntil: 'networkidle0',
      timeout: 10000 
    });
    
    // Wait a bit for hydration to complete
    await page.waitForTimeout(2000);
    
    // Check if the page loaded successfully
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);
    
    // Check for hydration success indicators
    const hydrationSuccess = await page.evaluate(() => {
      return window.__VUE__ !== undefined;
    });
    
    if (hydrationSuccess) {
      console.log('✅ Vue app appears to be hydrated successfully');
    } else {
      console.log('⚠️  Vue app hydration status unclear');
    }
    
    // Test navigation to trigger route changes
    console.log('🔄 Testing route navigation...');
    await page.click('a[href="/detail"]').catch(() => {
      console.log('ℹ️  Detail link not found, skipping navigation test');
    });
    
    await page.waitForTimeout(1000);
    
    console.log('✅ Hydration test completed');
    
  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Check if puppeteer is available
try {
  require.resolve('puppeteer');
  testHydration();
} catch (e) {
  console.log('ℹ️  Puppeteer not found. Install it with: npm install puppeteer');
  console.log('ℹ️  Or manually test by:');
  console.log('   1. Run: npm run dev:ssr');
  console.log('   2. Open: http://localhost:8887');
  console.log('   3. Check browser console for hydration messages');
}
