{"version": 3, "file": "env.mjs", "sources": ["../../src/client/env.ts"], "sourcesContent": ["declare const __MODE__: string\ndeclare const __DEFINES__: Record<string, any>\n\nconst context = (() => {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis\n  } else if (typeof self !== 'undefined') {\n    return self\n  } else if (typeof window !== 'undefined') {\n    return window\n  } else {\n    return Function('return this')()\n  }\n})()\n\n// assign defines\nconst defines = __DEFINES__\nObject.keys(defines).forEach((key) => {\n  const segments = key.split('.')\n  let target = context\n  for (let i = 0; i < segments.length; i++) {\n    const segment = segments[i]\n    if (i === segments.length - 1) {\n      target[segment] = defines[key]\n    } else {\n      target = target[segment] || (target[segment] = {})\n    }\n  }\n})\n"], "names": [], "mappings": "AAGA,MAAM,OAAO,GAAG,CAAC;IACf,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;QACrC,OAAO,UAAU,CAAA;KAClB;SAAM,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QACtC,OAAO,IAAI,CAAA;KACZ;SAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACxC,OAAO,MAAM,CAAA;KACd;SAAM;QACL,OAAO,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAA;KACjC;AACH,CAAC,GAAG,CAAA;AAEJ;AACA,MAAM,OAAO,GAAG,WAAW,CAAA;AAC3B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG;IAC/B,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC/B,IAAI,MAAM,GAAG,OAAO,CAAA;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;QAC3B,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAA;SAC/B;aAAM;YACL,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;SACnD;KACF;AACH,CAAC,CAAC"}