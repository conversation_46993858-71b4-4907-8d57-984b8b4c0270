declare module 'connect' {
  const connect: () => any
  export = connect
}

declare module 'cors' {
  function cors(options: any): any
  export = cors
}

declare module 'selfsigned' {
  export function generate(attrs: any, options: any, done?: any): any
}

declare module 'http-proxy' {
  const proxy: any
  export = proxy
}

declare module 'connect-history-api-fallback' {
  const plugin: any
  export = plugin
}

declare module 'launch-editor-middleware' {
  const plugin: any
  export = plugin
}

declare module 'postcss-load-config' {
  import type { Plugin, ProcessOptions } from 'postcss'
  function load(
    inline: any,
    root: string
  ): Promise<{
    options: ProcessOptions
    plugins: Plugin[]
  }>
  export = load
}

declare module 'postcss-import' {
  import type { Plugin } from 'postcss'
  const plugin: (options: {
    resolve: (
      id: string,
      basedir: string,
      importOptions: any
    ) => string | string[] | Promise<string | string[]>
  }) => Plugin
  export = plugin
}

declare module 'postcss-modules' {
  import type { Plugin } from 'postcss'
  const plugin: (options: any) => Plugin
  export = plugin
}

declare module '@rollup/plugin-dynamic-import-vars' {
  import type { Plugin } from 'rollup'

  interface Options {
    include?: string | RegExp | (string | RegExp)[]
    exclude?: string | RegExp | (string | RegExp)[]
    warnOnError?: boolean
  }

  const p: (o?: Options) => Plugin
  export default p
}

declare module 'rollup-plugin-web-worker-loader' {
  import type { Plugin } from 'rollup'

  interface Options {
    targetPlatform?: string
    pattern?: RegExp
    extensions?: string[]
    sourcemap?: boolean
    inline?: boolean
  }

  const p: (o?: Options) => Plugin
  export default p
}

declare module 'micromatch' {
  export function isMatch(
    path: string,
    pattern: string,
    options?: { matchBase?: boolean }
  ): boolean
}

// LESS' types somewhat references this which doesn't make sense in Node,
// so we have to shim it
declare interface HTMLLinkElement {}
