{"name": "vite", "version": "2.9.18", "license": "MIT", "author": "<PERSON>", "description": "Native-ESM powered web dev build tool", "bin": {"vite": "bin/vite.js"}, "main": "dist/node/index.js", "types": "dist/node/index.d.ts", "files": ["bin", "dist", "client.d.ts", "src/client", "types"], "engines": {"node": ">=12.2.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/vite"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/#readme", "scripts": {"dev": "rimraf dist && rollup -c -w", "build": "rimraf dist && npm run lint && run-s build-bundle build-types", "build-bundle": "rollup -c", "build-types": "run-s build-temp-types patch-types roll-types", "build-temp-types": "tsc --emitDeclarationOnly --outDir temp/node -p src/node", "ci-build": "rimraf dist && run-s build-bundle build-types", "patch-types": "ts-node scripts/patchTypes.ts", "roll-types": "api-extractor run && rimraf temp", "lint": "eslint --ext .ts src/**", "format": "prettier --write --parser typescript \"src/**/*.ts\"", "prepublishOnly": "npm run build"}, "//": "READ CONTRIBUTING.md to understand what to put under deps vs. devDeps!", "dependencies": {"esbuild": "^0.14.27", "postcss": "^8.4.13", "resolve": "^1.22.0", "rollup": ">=2.59.0 <2.78.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@ampproject/remapping": "^2.2.0", "@babel/parser": "^7.17.10", "@babel/types": "^7.17.10", "@jridgewell/trace-mapping": "^0.3.9", "@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-commonjs": "^21.1.0", "@rollup/plugin-dynamic-import-vars": "^1.4.3", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "^8.3.2", "@rollup/pluginutils": "^4.2.1", "@types/convert-source-map": "^1.5.2", "@types/cross-spawn": "^6.0.2", "@types/debug": "^4.1.7", "@types/estree": "^0.0.51", "@types/etag": "^1.8.1", "@types/less": "^3.0.3", "@types/micromatch": "^4.0.2", "@types/mime": "^2.0.3", "@types/node": "^17.0.25", "@types/resolve": "^1.20.2", "@types/sass": "~1.43.1", "@types/stylus": "^0.48.37", "@types/ws": "^8.5.3", "@vue/compiler-dom": "^3.2.33", "acorn": "^8.7.1", "cac": "6.7.9", "chokidar": "^3.5.3", "connect": "^3.7.0", "connect-history-api-fallback": "^1.6.0", "convert-source-map": "^1.8.0", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "dotenv": "^14.3.2", "dotenv-expand": "^5.1.0", "es-module-lexer": "^0.10.5", "estree-walker": "^2.0.2", "etag": "^1.8.1", "fast-glob": "^3.2.11", "http-proxy": "^1.18.1", "json5": "^2.2.1", "launch-editor-middleware": "^2.3.0", "magic-string": "^0.26.1", "micromatch": "^4.0.5", "mrmime": "^1.0.0", "node-forge": "^1.3.1", "okie": "^1.0.1", "open": "^8.4.0", "periscopic": "^2.0.3", "picocolors": "^1.0.0", "postcss-import": "^14.1.0", "postcss-load-config": "^3.1.4", "postcss-modules": "^4.3.1", "resolve.exports": "^1.1.0", "rollup-plugin-license": "^2.7.0", "sirv": "^2.0.2", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "strip-ansi": "^6.0.1", "strip-literal": "^0.2.0", "terser": "^5.13.1", "tsconfck": "^1.2.2", "tslib": "^2.4.0", "types": "link:./types", "ws": "^8.6.0"}, "peerDependencies": {"less": "*", "sass": "*", "stylus": "*"}, "peerDependenciesMeta": {"sass": {"optional": true}, "stylus": {"optional": true}, "less": {"optional": true}}}