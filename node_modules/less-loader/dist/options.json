{"type": "object", "properties": {"lessOptions": {"description": "Options to pass through to `Less` (https://github.com/webpack-contrib/less-loader#lessoptions).", "anyOf": [{"type": "object", "additionalProperties": true}, {"instanceof": "Function"}]}, "additionalData": {"description": "Prepends/Appends `Less` code to the actual entry file (https://github.com/webpack-contrib/less-loader#additionalData).", "anyOf": [{"type": "string"}, {"instanceof": "Function"}]}, "sourceMap": {"description": "Enables/Disables generation of source maps (https://github.com/webpack-contrib/less-loader#sourcemap).", "type": "boolean"}, "webpackImporter": {"description": "Enables/Disables default `webpack` importer (https://github.com/webpack-contrib/less-loader#webpackimporter).", "type": "boolean"}, "implementation": {"description": "The implementation of the `Less` to be used (https://github.com/webpack-contrib/less-loader#implementation).", "type": "object"}}, "additionalProperties": false}