{"name": "less-loader", "version": "7.3.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": "webpack-contrib/less-loader", "author": "<PERSON> @jhnns", "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": "https://github.com/webpack-contrib/less-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"klona": "^2.0.4", "loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/defaults": "^6.3.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^26.6.3", "cross-env": "^7.0.3", "del": "^6.0.0", "del-cli": "^3.0.1", "eslint": "^7.18.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.3.0", "husky": "^4.3.8", "jest": "^26.6.3", "less": "^4.1.0", "lint-staged": "^10.5.3", "memfs": "^3.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "standard-version": "^9.1.0", "strip-ansi": "^6.0.0", "webpack": "^5.16.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"]}