{"name": "inquirer", "version": "7.3.3", "description": "A collection of common interactive command line user interfaces.", "author": "<PERSON> <<EMAIL>>", "files": ["lib", "README.md"], "main": "lib/inquirer.js", "keywords": ["command", "prompt", "stdin", "cli", "tty", "menu"], "engines": {"node": ">=8.0.0"}, "devDependencies": {"chai": "^4.2.0", "chalk-pipe": "^4.0.0", "cmdify": "^0.0.4", "mocha": "^8.0.1", "mockery": "^2.1.0", "nyc": "^15.0.0", "sinon": "^9.0.0"}, "scripts": {"test": "nyc mocha test/**/* -r ./test/before", "posttest": "nyc report --reporter=text-lcov > ../../coverage/nyc-report.lcov", "prepublishOnly": "cp ../../README.md .", "postpublish": "rm -f README.md"}, "repository": "SBoudrias/Inquirer.js", "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.19", "mute-stream": "0.0.8", "run-async": "^2.4.0", "rxjs": "^6.6.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}, "gitHead": "808d5538531c1ca1c34f832d77bc98dfd0ba4000"}