{"name": "globals", "version": "12.4.0", "description": "Global identifiers from different JavaScript environments", "license": "MIT", "repository": "sindresorhus/globals", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "index.d.ts", "globals.json"], "keywords": ["globals", "global", "identifiers", "variables", "vars", "j<PERSON>t", "eslint", "environments"], "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"ava": "^2.2.0", "tsd": "^0.9.0", "xo": "^0.25.3"}, "xo": {"ignores": ["get-browser-globals.js"]}, "tsd": {"compilerOptions": {"resolveJsonModule": true}}}