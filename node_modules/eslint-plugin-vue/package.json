{"name": "eslint-plugin-vue", "version": "7.20.0", "description": "Official ESLint plugin for Vue.js", "main": "lib/index.js", "scripts": {"new": "node tools/new-rule.js", "start": "npm run test:base -- --watch --growl", "test:base": "mocha \"tests/lib/**/*.js\" --reporter dot", "test": "nyc npm run test:base -- \"tests/integrations/*.js\" --timeout 60000", "debug": "mocha --inspect \"tests/lib/**/*.js\" --reporter dot --timeout 60000", "cover": "npm run cover:test && npm run cover:report", "cover:test": "nyc npm run test:base -- --timeout 60000", "cover:report": "nyc report --reporter=html", "lint": "eslint . --rulesdir eslint-internal-rules", "lint:fix": "eslint . --rulesdir eslint-internal-rules --fix", "tsc": "tsc", "preversion": "npm test && git add .", "version": "env-cmd -e version npm run update && npm run lint -- --fix && git add .", "update": "node ./tools/update.js", "docs:watch": "vuepress dev docs", "predocs:build": "npm run update", "docs:build": "vuepress build docs"}, "files": ["lib"], "homepage": "https://eslint.vuejs.org", "keywords": ["eslint", "eslint-plugin", "eslint-config", "vue", "v<PERSON><PERSON><PERSON>", "rules"], "author": "<PERSON><PERSON> (https://github.com/mysticatea)", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/michalsnik)", "<PERSON><PERSON> (https://github.com/ota-meshi)"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/eslint-plugin-vue.git"}, "bugs": {"url": "https://github.com/vuejs/eslint-plugin-vue/issues"}, "engines": {"node": ">=8.10"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0"}, "dependencies": {"eslint-utils": "^2.1.0", "natural-compare": "^1.4.0", "semver": "^6.3.0", "vue-eslint-parser": "^7.10.0"}, "devDependencies": {"@types/eslint": "^7.28.1", "@types/eslint-visitor-keys": "^1.0.0", "@types/natural-compare": "^1.4.0", "@types/node": "^13.13.5", "@types/semver": "^7.2.0", "@typescript-eslint/parser": "^5.0.0-0", "@vuepress/plugin-pwa": "^1.4.1", "acorn": "^8.5.0", "env-cmd": "^10.1.0", "eslint": "^8.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-eslint-plugin": "^3.5.3", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "file:.", "espree": "^9.0.0", "lodash": "^4.17.21", "mocha": "^7.1.2", "nyc": "^15.1.0", "prettier": "^2.4.1", "typescript": "^4.4.3", "vue-eslint-editor": "^1.1.0", "vuepress": "^1.8.2"}}